<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" id="csrf-token" content="{{ csrf_token() }}">
    <title>{{ config('app.name', 'iPay BD Payment') }}</title>

    <link href="{{ asset('static/backend/css/bootstrap.min.css') }}" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" />



    <style>


         /* Disables all mouse interactions */

        body {
            font-size: 14px;
            color: #4c5258;
            letter-spacing: .5px;
            background: #f4f4f4;
            overflow-x: hidden;
            font-family: Roboto, sans-serif;

        }

        .wallet-wrap {
            padding-bottom: 1.5rem;
            margin: 5px auto;
        }

        .logo-wrap {
            display: grid;
            padding-bottom: 1.5rem;
            gap: 1rem;
            /* Auto-fit logos responsively */
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            margin: 30px auto;
        }

        .bank-img {
            margin-left: auto;
            margin-right: auto;
            height: 70%;
            width: 100%;
            /* -o-object-fit: cover; */
            object-fit: contain;
            transition-property: all;
            transition-timing-function: cubic-bezier(.4, 0, .2, 1);
            transition-duration: 300ms;
        }

        .bank-img-div {
            border-radius: 0.5rem;
            display: flex;
            height: 4.5rem;
            justify-content: center;
            overflow: hidden;
            padding: 6px;
            border: 1px solid #e1e1e1;
            align-items: center;
            background: #fff;
            transition: box-shadow .2s ease, transform .2s ease;
        }
        .bank-img-div:hover,
        .bank-img-div:focus {
            box-shadow: 0 6px 18px rgba(0,0,0,0.08);
            transform: translateY(-2px);
        }

        /* Styling for the loader */
        .loader {
            border: 4px solid #f3f3f3;
            /* Light grey */
            border-top: 4px solid #3498db;
            /* Blue */
            border-radius: 50%;
            width: 64px;
            height: 64px;
            animation: spin .5s linear infinite;
            display: none;
            /* Hide the loader by default */
            margin: 0 auto;
            margin-bottom: 16px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
        /* Payment switcher look */
        .payment-switcher {
            border-radius: .5rem;
            border: 1px solid #e1e1e1;
            background-color: #eee;
            cursor: pointer;
            user-select: none;
        }

        /* Responsive container widths */
        @media (max-width: 576px) {
            .card-body { padding: 1rem; }
            .bank-img-div { height: 4rem; }
        }

        /* Scalable large action buttons */
        .action-btn {
            width: 80%;
            font-weight: bold;
            padding: 6px 10px;
            font-size: clamp(1rem, 3.5vw + .25rem, 1.75rem);
        }
    </style>
</head>

<body>


    @php
        $app_name = app_config('AppName');
        $support_number = app_config('support_whatsapp_number');
        $image = app_config('AppLogo');
        $wallet_status = app_config('wallet_payment_status');
        $onlineCheckingTime = env('PAYMENT_TIME');
    @endphp



    <!-- Your content that will be populated by AJAX response -->
    <div id="content"></div>


    <div class="container">

        <div class="row row-cols-xl-2 my-5">
            <div class="col mx-auto">
                <div class="card mb-0">
                    <div class="card-body">
                        <div class="d-flex justify-content-end">
                            <a href="https://wa.me/{{ $support_number }}" target="_blank" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Chat with us on WhatsApp">
                                <img src="{{ asset('headset copy.png') }}" width="80" alt="WhatsApp">
                            </a>

                         </div>
                        <div class="p-4">
                            
                            @if (session()->has('alert'))
                                <div class="alert alert-success" id="alert_success">
                                    {{ session('alert') }}
                                </div>
                            @endif

                            {{-- <div class="mb-4 text-center">
                                <img src="{{ asset('storage/' . $image) }}" width="150" alt="">
                                <h2 class="text-primary mt-4">{{ $app_name }} Payment System</h2>
                                <input type="hidden" name="wallet_status" value="{{ $wallet_status }}"
                                    id="wallet_status">
                            </div> --}}

                            <div class="mb-4 text-center">
                                <img src="{{ asset('storage/' . $image) }}" width="150" alt="{{ $app_name }} logo" class="d-block mx-auto mb-3">
                                <div>
                                    <h2 class="d-inline text-primary mt-2">{{ $app_name }} Payment System</h2>
                                </div>
                                <input type="hidden" name="wallet_status" value="{{ $wallet_status }}" id="wallet_status">
                            </div>

                            <div class="col-12 col-md-9 mx-auto text-center">
                                <div class="row justify-content-center">
                                    @if ($wallet_status == 'true')
                                        <div class="w-50 p-3 bg-primary text-white fw-bold payment-switcher"
                                            attr-value="wallet">Wallet
                                            Money
                                        </div>
                                    @endif
                                    <div class="w-{{ $wallet_status == 'true' ? 50 : 100 }} p-3 text-black fw-bold payment-switcher"
                                        attr-value="mobile-banking">
                                        Mobile Banking
                                    </div>
                                </div>
                            </div>
                            
                           
                            <div class="wallet-wrap col-12 col-md-9 mx-auto" id="wallet-div">

                                <div class="form-group">
                                    <div id="loader" class="loader"></div>

                                    <input type="text" name="email_or_phone" id="mobile_input" required
                                        class="form-control" autofocus placeholder="Enter Account Mobile or Email"
                                        style="margin: 50px auto;height: 45px;" autocomplete="off">

                                    <input type="tel" id="otp_input" maxlength="6" inputmode="numeric" pattern="[0-9]*"
                                        class="form-control d-none" placeholder="Enter 6 digit OTP"
                                        style="margin: 50px auto;height: 45px;" autocomplete="one-time-code">

                                    

                                    <button type="button" class="btn btn-primary d-block w-100 mt-4"
                                        id="wallet-proceed">
                                        Proceed
                                    </button>

                                    <button type="button" class="btn btn-primary mt-2 d-block w-100 d-none"
                                        id="otp-proceed">
                                        Proceed
                                    </button>

                                    <div class="mt-5">
                                        <p class="text-center">If you don't have a account</p>
                                        <a class="btn btn-sm d-block fw-bold" style="font-size: 17px; color: #0d6efd"
                                            href="https://ipaybd.com/customer/create-account" target="_blank">Create
                                            Account</a>
                                    </div>
                                </div>

                            </div>

                            {{-- @dd($payment_request) --}}
                              {{--  @dd(getOpNameList())  --}}
                              <input type="hidden" value="{{ $payment_request->request_id }}"
                             name="request_id_input" id="request_id_input" autocomplete="off">


                            <div class="logo-wrap d-none col-12 col-md-9 mx-auto" id="mfs-operator-div">
                             
                            
                             @foreach ( mfsList() as $item )

                            @php
                                if($item['deposit_method'] == 'bkash'){

                                    $imagePath = 'payments/bkash.png';
                                }else if($item['deposit_method'] == 'nagad'){
                                    $imagePath = 'payments/nagad.png';
                                }else if($item['deposit_method'] == 'rocket'){
                                    $imagePath = 'payments/rocket.png';
                                }else if($item['deposit_method'] == 'upay'){
                                    $imagePath = 'payments/upay.png';
                                }
                            @endphp



                                    <a href='{{ url("/checkout/payment/$payment_request->request_id/" . $item['deposit_method']. "/". $item['deposit_number'] ) }}' class="bank-img-div">
                                        <img src="{{ asset($imagePath) }}" class="bank-img" />

                                    </a>

                            @endforeach
                            
                              
                                <!-- api merchant -->
                           
                                @foreach ($paymentMethods as $paymentMethod)
                                
                                @if ($paymentMethod->type == 'api')
                                
                                
                               
                                 <a href="#" class="bank-img-div sbmtLink" data-target="#form{{ $paymentMethod->id }}" aria-label="Pay with API method">
                                                            <img src="{{ asset($paymentMethod->mfs->image) }}" class="bank-img" alt="Payment method" loading="lazy" />
                                                           
                                                            <span class="text-bg-success">API</span>
                                                        </a>
                                    
                                                       <form id="form{{ $paymentMethod->id }}" style="visibility: hidden;" action="{{ route('merchant_api_submit') }}" method="POST">
                                                            @csrf
                                                            <input type="hidden" name="method_id" value="{{ $paymentMethod->id }}">
                                                            <input type="hidden" name="callback_url" value="{{ $payment_request->callback_url }}">
                                                            <input type="hidden" name="currency" value="{{ $payment_request->currency }}">
                                                            <input type="hidden" name="reference" value="{{ $payment_request->reference }}">
                                                            <input type="hidden" name="cust_name" value="{{ $payment_request->cust_name }}">
                                                            <input type="hidden" name="cust_phone" value="{{ $payment_request->cust_phone }}">
                                                            <input type="hidden" name="amount" value="{{ $payment_request->amount }}">
                                                            <input type="hidden" name="merchant_id" value="{{ $payment_request->merchant_id }}">
                                                            <button type="submit" class="submitFormButton" style="visibility: hidden;">Submit</button>
                                                        </form>
                                                        
                                                        @endif
                                                       
                                                  
                                    @endforeach
                                    
                                      <!-- api merchant finish -->


                            </div>

                            <div class="col-12 d-flex justify-content-center">
                                <button type="submit" class="btn action-btn"
                                    style="background: #D8DCFF; color: #0019FE;">
                                    Pay {{ number_format($payment_request->amount, 2) }} ৳
                                </button>
                            </div>

                            <div class="col-12 mt-4 d-flex justify-content-center">
                                <button type="button" class="btn action-btn" onClick="submitCancel('{{ $payment_request->request_id }}')"
                                    style="background: #f60303; color: #f2f2f9;">
                                    Cancel
                                </button>
                            </div>

                            @php
                            $paymentTime = env('PAGE_TIME');
                            @endphp

                            <div class="col-12">
                                <div class="d-grid">
                                    <p class="fw-bold text-center"><span data-duration="{{  $paymentTime }}" id="timer"></span></p>
                                    {{-- <p><a href="{{ url('checkout/'.$payment_request->trxid) }}">Back</a></p> --}}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="{{ asset('static/backend/js/jquery.min.js') }}"></script>
    <script src="{{ asset('static/backend/js/bootstrap.bundle.min.js') }}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>


    <script>
       $(document).ready(function () {
    // Bind click event to all links with class 'sbmtLink'
    $('.sbmtLink').on('click', function (e) {
        e.preventDefault(); // Prevent default link behavior

        // Get the target form ID from the 'data-target' attribute
        const targetForm = $(this).data('target');

        // Debugging: Log the target form
        console.log('Target form:', targetForm);

        // Check if the form exists
        if ($(targetForm).length) {
            // Find and trigger the hidden submit button within the target form
            $(targetForm).find('.submitFormButton').click();
        } else {
            console.error('Form not found:', targetForm);
        }
    });
});

    </script>

    <script>
        toastr.options.closeButton = true;
        toastr.options.showMethod = 'slideDown';
        toastr.options.closeMethod = 'slideUp';
        toastr.options.progressBar = true;
        // Function to show the loader
        function showLoader() {
            $("#loader").show();
        }

        // Function to hide the loader
        function hideLoader() {
            $("#loader").hide();
        }
        
       

        $(document).ready(function() {
           
            $('#submitLink').on('click', function(e) {
                e.preventDefault(); // Prevent default link behavior
                console.log(e);
                $('#submitFormButton').click(); // Trigger the hidden submit button
            });

            let get_wallet_status = $('#wallet_status').val();
            let check = 1;


            $(document).on('click', '.payment-switcher', function() {

                if (check == 1) {
                    $(this).addClass('bg-primary').addClass('text-white');
                    $(this).siblings().removeClass('bg-primary')
                        .addClass('text-black')
                        .removeClass('text-white');
                    let data = $(this).attr('attr-value');


                    if (data == 'wallet') {
                        $('.wallet-wrap').removeClass('d-none');
                        $('.logo-wrap').addClass('d-none');

                    } else if (data == 'mobile-banking') {

                        $('.wallet-wrap').addClass('d-none');
                        $('.logo-wrap').removeClass('d-none');
                    }
                    if (get_wallet_status == 'false') {
                        check = 2;

                    }
                } else {
                    return;
                }
            })

            function make_active() {
                if (get_wallet_status === 'true') {
                    $('.payment-switcher[attr-value="wallet"]').trigger('click');
                } else {
                    $('.payment-switcher[attr-value="mobile-banking"]').trigger('click');
                    // $('.wallet-wrap').addClass('d-none');
                    // $('.logo-wrap').removeClass('d-none');
                }
            }

            make_active();





            $(document).on('click', '#wallet-proceed', function() {
                let verifierInput = $('#mobile_input').val();
                if (verifierInput == '') {
                    toastr.warning('Please insert email or phone number of your wallet');
                    return;
                }

                $.ajax({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    beforeSend: function() {
                        showLoader(); // Show the loader before the AJAX request starts
                    },
                    url: '{{ url('checkout/payment/otp-send') }}', // Specify the URL of the server-side script or API endpoint
                    type: 'POST',
                    data: {
                        'input_value': verifierInput
                    },
                    success: function(response) {
                        hideLoader(); // Hide the loader after the AJAX request is completed
                        if (response.success === false) {
                            $.each(response.error, function(index, errorValue) {
                                toastr.warning(errorValue);
                            });
                        } else if (response.success === true) {
                            $.each(response.message, function(index, successValue) {
                                $('#mobile_input').addClass('d-none');
                                $('#otp_input').removeClass('d-none');
                                $('#wallet-proceed').addClass('d-none');
                                $('#otp-proceed').text('Verify Otp').removeClass(
                                    'd-none');
                                toastr.success(successValue);
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        toastr.error(error);
                        hideLoader(); // Hide the loader even in case of an error

                    }
                });
            })

            $(document).on('click', '#otp-proceed', function() {
                let otpInput = $('#otp_input').val();
                let mobile = $('#mobile_input').val();
                let request_id = $('#request_id_input').val();
                if (otpInput == '') {
                    toastr.warning('Otp should not be empty');
                    return;
                }

                if (otpInput.length < 6 || otpInput.length > 6) {
                    toastr.warning('Otp should be 6 characters long');
                    return;
                }

                $.ajax({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    beforeSend: function() {
                        showLoader(); // Show the loader before the AJAX request starts
                    },
                    url: '{{ url('checkout/payment/otp-verify') }}', // Specify the URL of the server-side script or API endpoint
                    type: 'POST',
                    data: {
                        'otp_value': otpInput,
                        'input_value': mobile,
                        'request_id': request_id,
                    },
                    success: function(response) {

                        if (response.success === false) {
                            hideLoader(); // Hide the loader after the AJAX request is completed
                            $.each(response.error, function(index, errorValue) {
                                toastr.warning(errorValue);
                            });
                        } else if (response.success == true && response.hasOwnProperty('url')) {
                            $(this).attr('disabled');
                            $.each(response.message, function(index, successValue) {
                                toastr.success(successValue);
                            });
                            hideLoader(); // Hide the loader after the AJAX request is completed
                            setTimeout(function(e) {
                                window.location.href = response.url;
                            }, 1000);
                        } else if (response.success === true) {
                            toastr.success(response);

                            $.each(response.message, function(index, successValue) {
                                toastr.success(successValue);
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        toastr.error(error);
                        hideLoader(); // Hide the loader after the AJAX request is completed
                    }
                });
            })

        })

        $(document).ready(function() {
            var timer = $('#timer');
            var req_id = document.getElementById("request_id_input").value;
            // console.log(req_id)


            var duration = timer.data('duration');
            let flag = true;

            var interval = setInterval(function() {
                let minutes = Math.floor(duration / 60);
                let seconds = duration % 60;

                minutes = minutes < 10 ? "0" + minutes : minutes;
                seconds = seconds < 10 ? "0" + seconds : seconds;

                timer.css("color", "red").text("Warning! This page will expired after " + minutes + ":" + seconds );


                if (--duration < 0) {
                    if(flag == true){
                      flag = false;

                    }

                    canceledRequest(req_id);


                }

            }, 1000);
        });


        function submitCancel(req_id) {
    Swal.fire({
        title: 'Are you sure?',
        text: "You won't be able to revert this!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire({
                title: 'Processing...',
                text: 'Please wait a moment.',
                allowOutsideClick: false,
                allowEscapeKey: false,
                allowEnterKey: false,
                didOpen: () => {
                    Swal.showLoading(); // Show the loading indicator
                }
            });

            $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                url: '{{ url('/checkout/payment/cancelled') }}',
                type: 'POST',
                data: {
                    'request_id': req_id,
                },
                success: function(response) {

                    Swal.close();
                    if (response.success === true) {

                        Swal.fire({
                            title: 'Completed!',
                            text: 'Your operation was successful.',
                            icon: 'success'
                        });

                        window.location.assign(response.url);
                    }
                },
                error: function(xhr, status, error) {
                    toastr.error(error);
                    Swal.close(); // Ensure the loader is hidden on error
                }
            });
        }
    });
}

function canceledRequest(req_id) {
    return $.ajax({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        url: '{{ url('/checkout/payment/cancelled') }}',
        type: 'POST',
        data: {
            'request_id': req_id
        },
        success: function(response) {
            console.log(response);

            if (response.success === false) {
                $.each(response.error, function(index, errorValue) {
                    toastr.warning(errorValue);
                });
                return; // Exit function if there's an error
            }

            if (response.success === true) {
                toastr.success(response);
                window.location.assign(response.url);
            } else {
                console.log(response); // Fallback logging for unexpected cases
            }
        },
        error: function(xhr, status, error) {
            toastr.error(error);
            hideLoader(); // Hide the loader after the AJAX request is completed
        }
    });
}


    </script>


    {{-- bkash --}}

    {{-- <script>
        document.getElementById('getToken').addEventListener('click', function() {
            const proxyUrl = 'https://cors-anywhere.herokuapp.com/';
            const url = 'https://tokenized.sandbox.bka.sh/v1.2.0-beta/tokenized/checkout/token/grant';
            const data = {
                app_key: '4f6o0cjiki2rfm34kfdadl1eqq',
                app_secret: '2is7hdktrekvrbljjh44ll3d9l1dtjo4pasmjvs5vl5qr3fug4b'
            };

            fetch(proxyUrl + url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'username': 'sandboxTokenizedUser02',
                        'password': 'sandboxTokenizedUser02@12345'
                    },
                    body: JSON.stringify(data)
                })
                .then(response => response.json())
                .then(data => {
                    console.log('Success:', data);
                })
                .catch((error) => {
                    console.error('Error:', error);
                });
        });
    </script> --}}

    {{-- <script src="{{ asset('static/backend/js/validation.js') }}"> </script> --}}

</body>

</html>
