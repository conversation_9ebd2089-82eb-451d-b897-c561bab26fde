<?php

namespace App\Service\Backend;
use App\Models\PaymentMethod;

class BkashService
{
    public function getActiveBkashMethod()
{
    $methods = PaymentMethod::where('type', 'api')
        ->where('status', 1)
        ->with('mfs_operator') // eager load
        ->get()
        ->groupBy(fn($method) => $method->mfs_operator->name ?? null) // group by operator name
        ->map(function ($group, $operatorName) {
            $method = $group->random(); // pick random PaymentMethod in this group

            return [
                'deposit_method' => $operatorName,
                'deposit_number' => $method->sim_id,
                'icon' => isset($method->mfs_operator->image) 
                    ? url('payments/' . $method->mfs_operator->image) 
                    : null,
                'type'=> 'P2C'
            ];
        })
        ->values(); // reset keys to 0,1,2...

    return $methods;
}


}