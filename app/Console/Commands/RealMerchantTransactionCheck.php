<?php

namespace App\Console\Commands;

use App\Models\PaymentRequest;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class RealMerchantTransactionCheck extends Command
{
    protected $signature = 'app:real-merchant-transaction-check';
    protected $description = 'Live merchant api check and that transaction success or not and something work on there.';

    public function handle()
    {
        $requests = PaymentRequest::where('status', 0)
            ->get();

    }


}