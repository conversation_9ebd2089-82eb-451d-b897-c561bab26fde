<?php

// Simple test to demonstrate the validation changes
require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\Validator;

// Test the old validation (would fail with string IDs)
echo "=== Testing OLD validation (integer only) ===\n";
$oldValidator = Validator::make(['id' => '123'], ['id' => 'required|integer']);
echo "String '123' with integer validation: " . ($oldValidator->fails() ? "FAILS" : "PASSES") . "\n";

$oldValidator2 = Validator::make(['id' => 123], ['id' => 'required|integer']);
echo "Integer 123 with integer validation: " . ($oldValidator2->fails() ? "FAILS" : "PASSES") . "\n";

echo "\n=== Testing NEW validation (numeric - accepts both) ===\n";
$newValidator = Validator::make(['id' => '123'], ['id' => 'required|numeric']);
echo "String '123' with numeric validation: " . ($newValidator->fails() ? "FAILS" : "PASSES") . "\n";

$newValidator2 = Validator::make(['id' => 123], ['id' => 'required|numeric']);
echo "Integer 123 with numeric validation: " . ($newValidator2->fails() ? "FAILS" : "PASSES") . "\n";

$newValidator3 = Validator::make(['id' => 'abc'], ['id' => 'required|numeric']);
echo "String 'abc' with numeric validation: " . ($newValidator3->fails() ? "FAILS" : "PASSES") . "\n";

echo "\n=== Summary ===\n";
echo "✅ Changed validation from 'required|integer' to 'required|numeric'\n";
echo "✅ Now accepts both integer and string representations of numbers\n";
echo "✅ Still rejects non-numeric strings\n";
echo "✅ Added explicit integer casting when using the ID in database queries\n";
